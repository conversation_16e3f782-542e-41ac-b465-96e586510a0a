#!/usr/bin/env python3
"""
Django MongoDB Setup Script

This script helps you set up the Django-MongoDB integration
and sync existing Django users with MongoDB.
"""

import os
import sys

def setup_django_mongodb():
    """Setup Django-MongoDB integration"""
    
    print("🚀 DJANGO-MONGODB INTEGRATION SETUP")
    print("=" * 50)
    
    print("\n📋 SETUP CHECKLIST:")
    print("1. ✅ MongoDB connection configured")
    print("2. ✅ Django views updated for MongoDB")
    print("3. ✅ Management command created")
    print("4. ✅ Integration module created")
    
    print("\n🔧 NEXT STEPS:")
    print("=" * 30)
    
    print("\n1. 📡 Test MongoDB connection:")
    print("   python config.py")
    
    print("\n2. 🔄 Sync Django users with MongoDB:")
    print("   cd bookrecommender")
    print("   python manage.py sync_mongodb --create-profiles")
    
    print("\n3. 👀 View synced data:")
    print("   python manage.py sync_mongodb --show-users")
    
    print("\n4. 🌐 Run Django server:")
    print("   python manage.py runserver")
    
    print("\n5. 📊 Check MongoDB Compass:")
    print("   - Connect to: mongodb://localhost:27017")
    print("   - Database: readinglist_app")
    print("   - Collections: django_users, books, readlist, viewed_books")
    
    print("\n🎯 WHAT WILL HAPPEN:")
    print("=" * 30)
    print("✅ Django users → MongoDB profiles")
    print("✅ User registration → Auto MongoDB profile")
    print("✅ Book viewing → MongoDB storage")
    print("✅ Reading lists → MongoDB storage")
    print("✅ Complete book details → Auto-enhanced")
    
    print("\n📱 DJANGO VIEWS UPDATED:")
    print("=" * 30)
    print("✅ /register/ → Creates MongoDB profile")
    print("✅ /profile/ → Shows MongoDB data")
    print("✅ /readlist/ → MongoDB reading list")
    print("✅ /recommendations/ → MongoDB-based")
    print("✅ /view-book/ → Records in MongoDB")
    print("✅ /add-to-readlist/ → Stores in MongoDB")
    
    print("\n🔍 TROUBLESHOOTING:")
    print("=" * 30)
    print("❓ Django users not in MongoDB?")
    print("   → Run: python manage.py sync_mongodb --create-profiles")
    print("")
    print("❓ Import errors?")
    print("   → Make sure you're in the project root directory")
    print("")
    print("❓ MongoDB connection issues?")
    print("   → Check if MongoDB is running: python config.py")
    
    print("\n🎉 READY TO GO!")
    print("Your Django app is now integrated with MongoDB!")
    print("Users, books, and reading data will be stored in MongoDB.")

def create_test_data():
    """Create some test data for demonstration"""
    
    print("\n🧪 CREATING TEST DATA:")
    print("=" * 30)
    
    # This would create test users and books
    print("Test data creation would happen here...")
    print("(Run the Django management command instead)")

if __name__ == "__main__":
    setup_django_mongodb()
    
    print("\n" + "="*50)
    print("🚀 QUICK START COMMANDS:")
    print("="*50)
    print("cd bookrecommender")
    print("python manage.py sync_mongodb --create-profiles")
    print("python manage.py runserver")
    print("# Then open: http://127.0.0.1:8000")
    print("="*50)
