"""
Django Management Command: Sync MongoDB

This command syncs Django users with MongoDB and sets up the integration.
"""

from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
import sys
import os

# Add the parent directory to Python path to import our MongoDB modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))))

try:
    from mongodb_django_integration import MongoDBUserManager, sync_all_django_users
    from config import test_connection
except ImportError as e:
    print(f"Import error: {e}")
    print("Make sure you're running this from the correct directory with MongoDB modules available")

class Command(BaseCommand):
    help = 'Sync Django users with MongoDB and set up integration'

    def add_arguments(self, parser):
        parser.add_argument(
            '--create-profiles',
            action='store_true',
            help='Create MongoDB profiles for all Django users',
        )
        parser.add_argument(
            '--show-users',
            action='store_true',
            help='Show all Django users and their MongoDB status',
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('🔗 MongoDB Django Integration')
        )
        self.stdout.write('=' * 50)
        
        # Test MongoDB connection
        self.stdout.write('\n📡 Testing MongoDB connection...')
        if not test_connection():
            self.stdout.write(
                self.style.ERROR('❌ MongoDB connection failed!')
            )
            return
        
        self.stdout.write(
            self.style.SUCCESS('✅ MongoDB connection successful!')
        )
        
        # Show Django users
        django_users = User.objects.all()
        self.stdout.write(f'\n👥 Found {django_users.count()} Django users:')
        
        for user in django_users:
            self.stdout.write(f'  📱 {user.username} ({user.email}) - Joined: {user.date_joined}')
        
        if options['show_users']:
            self.show_user_mongodb_status(django_users)
        
        if options['create_profiles']:
            self.create_mongodb_profiles(django_users)
        
        if not options['create_profiles'] and not options['show_users']:
            self.stdout.write('\n💡 Available options:')
            self.stdout.write('  --create-profiles: Create MongoDB profiles for Django users')
            self.stdout.write('  --show-users: Show MongoDB status for all users')
            self.stdout.write('\n📝 Example: python manage.py sync_mongodb --create-profiles')
    
    def show_user_mongodb_status(self, django_users):
        """Show MongoDB status for each Django user"""
        self.stdout.write('\n📊 MongoDB Status for Django Users:')
        self.stdout.write('-' * 50)
        
        for user in django_users:
            profile = MongoDBUserManager.get_user_profile(user)
            if profile:
                self.stdout.write(
                    self.style.SUCCESS(f'✅ {user.username}: MongoDB profile exists')
                )
                self.stdout.write(f'   📅 Created: {profile.get("created_at")}')
                self.stdout.write(f'   📚 Reading goal: {profile.get("profile", {}).get("reading_goal", 0)}')
            else:
                self.stdout.write(
                    self.style.WARNING(f'⚠️ {user.username}: No MongoDB profile')
                )
    
    def create_mongodb_profiles(self, django_users):
        """Create MongoDB profiles for Django users"""
        self.stdout.write('\n🔄 Creating MongoDB profiles...')
        self.stdout.write('-' * 50)
        
        created_count = 0
        updated_count = 0
        
        for user in django_users:
            result = MongoDBUserManager.create_user_profile(user)
            if "created" in result:
                created_count += 1
                self.stdout.write(
                    self.style.SUCCESS(f'✅ Created profile for {user.username}')
                )
            else:
                updated_count += 1
                # Sync existing profile
                MongoDBUserManager.sync_django_user(user)
                self.stdout.write(
                    self.style.WARNING(f'🔄 Synced existing profile for {user.username}')
                )
        
        self.stdout.write('\n📊 Summary:')
        self.stdout.write(f'  ✅ Created: {created_count} new profiles')
        self.stdout.write(f'  🔄 Synced: {updated_count} existing profiles')
        self.stdout.write(f'  📱 Total: {created_count + updated_count} users processed')
        
        self.stdout.write('\n🎉 MongoDB integration complete!')
        self.stdout.write('✅ Django users are now synced with MongoDB')
        self.stdout.write('✅ Check MongoDB Compass to see user profiles')
