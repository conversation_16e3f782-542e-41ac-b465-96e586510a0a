from config import test_connection, get_collections
from user import register, login
from book import add_book
from readlist import add_to_readlist, get_readlist, update_status, remove_from_readlist, add_movie_to_readlist
from recommend import recommend_by_author
from viewed import add_viewed_book

# Test MongoDB connection
if not test_connection():
    print("Exiting due to MongoDB connection failure")
    exit(1)

# Get database collections
users, books, readlist, viewed_books = get_collections()

if not all([users, books, readlist, viewed_books]):
    print("Failed to get database collections")
    exit(1)

# Register and login with enhanced user details
print("=== Enhanced MongoDB Storage Demo ===")
print(register("alice", "password123", email="<EMAIL>", full_name="<PERSON>"))
print(login("alice", "password123"))

# Add a book with complete details
print("\n--- Adding Book with Complete Details ---")
add_book(
    isbn="1234567890",
    title="Sample Book",
    author="Author Name",
    cover_url="http://example.com/cover.jpg",
    description="A fascinating sample book for testing purposes.",
    genre="Technology",
    publication_year=2023,
    publisher="Sample Publishers",
    page_count=250,
    rating=4.2
)

# Simulate viewing the book with complete details
print("\n--- Viewing Book (Complete Details Stored) ---")
complete_book_data = {
    "isbn": "1234567890",
    "title": "Sample Book",
    "author": "Author Name",
    "cover_url": "http://example.com/cover.jpg",
    "description": "A fascinating sample book for testing purposes.",
    "genre": "Technology",
    "publication_year": 2023,
    "publisher": "Sample Publishers",
    "page_count": 250,
    "rating": 4.2
}
print(add_viewed_book("alice", complete_book_data))

# Add to reading list with complete book details
print("\n--- Adding to Reading List (Complete Details Stored) ---")
print(add_to_readlist("alice", complete_book_data))

# Get reading list (now contains complete book details)
print("\n--- Reading List with Complete Details ---")
readlist = get_readlist("alice")
for item in readlist:
    if item.get('type') == 'book':
        print(f"Book: {item.get('title')} by {item.get('author')}")
        print(f"  Genre: {item.get('genre')}, Pages: {item.get('page_count')}")
        print(f"  Status: {item.get('status')}, Added: {item.get('date_added')}")

# Update status
print("\n--- Updating Reading Status ---")
print(update_status("alice", "1234567890", "completed"))

# Recommend related books
print("\n--- Book Recommendations ---")
print(recommend_by_author("1234567890"))

# Add a movie to reading list
print("\n--- Adding Movie to Reading List ---")
print(add_movie_to_readlist("alice", "Inception", "Christopher Nolan"))

print("\n=== Demo Complete ===")
print("✅ All data stored with complete details in MongoDB!")