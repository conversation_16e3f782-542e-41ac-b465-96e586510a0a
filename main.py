from config import test_connection, get_collections
from user import register, login
from readlist import add_to_readlist, get_readlist
from viewed import add_viewed_book, get_viewed_books

# Test MongoDB connection
if not test_connection():
    print("Exiting due to MongoDB connection failure")
    exit(1)

# Get database collections
users, books, readlist, viewed_books = get_collections()

if not all([users, books, readlist, viewed_books]):
    print("Failed to get database collections")
    exit(1)

# PROBLEM FIXED: Auto-Enhancement Demo
print("🎯 PROBLEM FIXED: AUTO-ENHANCEMENT DEMO")
print("=" * 60)
print("Now works with minimal book data!")
print("=" * 60)

# Create a real user
print("\n👤 Creating user account...")
register_result = register(
    username="reader",
    password="mypassword",
    email="<EMAIL>",
    full_name="Jane Reader",
    preferences={"reading_goal": 25}
)
print(f"Registration: {register_result}")

login_result = login("reader", "mypassword")
print(f"Login: {'Success' if login_result else 'Failed'}")

# REAL USAGE: Minimal book data (like you probably have)
minimal_book = {
    "isbn": "*************",
    "title": "Sapiens: A Brief History of Humankind",
    "author": "Yuval Noah Harari"
    # No genre, pages, rating, description, etc.
}

print(f"\n📖 REAL USAGE: User views book with minimal data")
print(f"Input: {minimal_book}")
view_result = add_viewed_book("reader", minimal_book)
print(f"✅ {view_result}")

print(f"\n📋 REAL USAGE: User adds to readlist with minimal data")
readlist_result = add_to_readlist("reader", minimal_book, "to_read")
print(f"✅ {readlist_result}")

# Show what was actually stored (auto-enhanced!)
print(f"\n📊 WHAT WAS ACTUALLY STORED (AUTO-ENHANCED):")
print("-" * 50)

viewed_books = get_viewed_books("reader")
if viewed_books:
    book = viewed_books[0]
    print(f"📚 Viewed Book Details:")
    print(f"  📖 Title: {book['title']}")
    print(f"  ✍️ Author: {book['author']}")
    print(f"  🏷️ Genre: {book['genre']} (auto-detected!)")
    print(f"  📄 Pages: {book['page_count']} (default)")
    print(f"  ⭐ Rating: {book['rating']} (default)")
    print(f"  📝 Description: {book['description']}")

readlist = get_readlist("reader")
if readlist:
    item = readlist[0]
    if item.get('type') == 'book':
        print(f"\n📋 Readlist Item Details:")
        print(f"  📖 Title: {item['title']}")
        print(f"  📊 Status: {item['status']}")
        print(f"  🏷️ Genre: {item['genre']} (auto-detected!)")
        print(f"  📄 Pages: {item['page_count']} (default)")
        print(f"  ⭐ Rating: {item['rating']} (default)")

print(f"\n" + "=" * 60)
print("✅ PROBLEM SOLVED!")
print("✅ Functions now work with minimal book data:")
print("   📖 add_viewed_book(username, minimal_book)")
print("   📋 add_to_readlist(username, minimal_book)")
print("")
print("✅ Auto-enhancement provides:")
print("   🏷️ Genre detection from title")
print("   📄 Default page count (250)")
print("   ⭐ Default rating (4.0)")
print("   📝 Generated description")
print("   🏢 Default publisher")
print("   📅 Default publication year")
print("=" * 60)