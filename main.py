from config import test_connection, get_collections
from user import register, login
from readlist import add_to_readlist, get_readlist, update_status
from viewed import add_viewed_book, get_viewed_books

# Test MongoDB connection
if not test_connection():
    print("Exiting due to MongoDB connection failure")
    exit(1)

# Get database collections
users, books, readlist, viewed_books = get_collections()

if not all([users, books, readlist, viewed_books]):
    print("Failed to get database collections")
    exit(1)

# Real user scenario - no test cases
print("📚 REAL BOOK TRACKING SYSTEM")
print("=" * 50)
print("Demonstrating complete book details storage")
print("=" * 50)

# Create a real user
print("\n👤 Creating user account...")
register_result = register(
    username="reader",
    password="mypassword",
    email="<EMAIL>",
    full_name="Jane Reader",
    preferences={"reading_goal": 25}
)
print(f"Registration: {register_result}")

login_result = login("reader", "mypassword")
print(f"Login: {'Success' if login_result else 'Failed'}")

# Real book that user discovers
real_book = {
    "isbn": "*************",
    "title": "Sapiens: A Brief History of Humankind",
    "author": "Yuval <PERSON>",
    "cover_url": "https://images.example.com/sapiens.jpg",
    "description": "An exploration of how Homo sapiens came to dominate the world.",
    "genre": "History",
    "publication_year": 2014,
    "publisher": "Harper",
    "page_count": 443,
    "rating": 4.4
}

# User discovers and views the book
print(f"\n📖 User views: '{real_book['title']}'")
view_result = add_viewed_book("reader", real_book)
print(f"✅ {view_result}")
print(f"📊 Complete details stored: {real_book['genre']}, {real_book['page_count']} pages, ⭐{real_book['rating']}")

# User adds book to reading list
print(f"\n📋 User adds book to reading list...")
readlist_result = add_to_readlist("reader", real_book, "to_read")
print(f"✅ {readlist_result}")

# Show stored data
print(f"\n📈 User's viewing history:")
viewed_books = get_viewed_books("reader")
for book in viewed_books:
    print(f"  📚 {book['title']} by {book['author']}")
    print(f"     Genre: {book['genre']} | Pages: {book['page_count']} | Rating: ⭐{book['rating']}")

print(f"\n📋 User's reading list:")
readlist = get_readlist("reader")
for item in readlist:
    if item.get('type') == 'book':
        print(f"  📖 {item['title']} by {item['author']}")
        print(f"     Status: {item['status']} | Genre: {item['genre']} | Pages: {item['page_count']}")

# User updates reading progress
print(f"\n📝 User starts reading...")
update_result = update_status("reader", real_book['isbn'], "reading")
print(f"✅ {update_result}")

print(f"\n" + "=" * 50)
print("✅ REAL USAGE COMPLETE!")
print("✅ Complete book details stored when user:")
print("   📖 Views books")
print("   📋 Adds to reading list")
print("   📊 All metadata preserved automatically")
print("=" * 50)