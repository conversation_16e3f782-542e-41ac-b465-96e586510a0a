#!/usr/bin/env python3
"""
Real-World Book Viewing Demo

This script demonstrates how the enhanced MongoDB storage works
when users view books in a real application scenario.
"""

from config import test_connection
from user import register, login, get_user_profile, update_reading_stats
from book import add_book, get_book
from viewed import add_viewed_book, get_viewed_books, get_recently_viewed
from readlist import add_to_readlist, get_readlist

def simulate_user_book_viewing():
    """Simulate a user browsing and viewing books"""
    
    print("🔗 Connecting to MongoDB...")
    if not test_connection():
        print("❌ Failed to connect to MongoDB")
        return
    
    print("\n👤 Creating user account...")
    # Register a new user
    register("reader123", "mypassword", 
             email="<EMAIL>", 
             full_name="Sarah Reader",
             preferences={"reading_goal": 24})
    
    login("reader123", "mypassword")
    print("✅ User 'reader123' logged in")
    
    # Sample books that user might view
    books_to_view = [
        {
            "isbn": "*************",
            "title": "The Alchemist",
            "author": "<PERSON>",
            "cover_url": "https://images.example.com/alchemist.jpg",
            "description": "A magical story about following your dreams and listening to your heart.",
            "genre": "Fiction",
            "publication_year": 1988,
            "publisher": "HarperOne",
            "page_count": 163,
            "rating": 4.1
        },
        {
            "isbn": "*************",
            "title": "The Lord of the Rings",
            "author": "J.R.R. Tolkien",
            "cover_url": "https://images.example.com/lotr.jpg",
            "description": "An epic fantasy adventure in Middle-earth.",
            "genre": "Fantasy",
            "publication_year": 1954,
            "publisher": "Houghton Mifflin",
            "page_count": 1216,
            "rating": 4.7
        },
        {
            "isbn": "9780525478812",
            "title": "The Seven Husbands of Evelyn Hugo",
            "author": "Taylor Jenkins Reid",
            "cover_url": "https://images.example.com/evelyn.jpg",
            "description": "A reclusive Hollywood icon finally tells her story.",
            "genre": "Historical Fiction",
            "publication_year": 2017,
            "publisher": "Atria Books",
            "page_count": 400,
            "rating": 4.6
        }
    ]
    
    print("\n📚 User browsing and viewing books...")
    
    # Simulate user viewing books
    for i, book in enumerate(books_to_view, 1):
        print(f"\n--- Viewing Book {i}: {book['title']} ---")
        
        # User views the book - complete details stored in MongoDB
        result = add_viewed_book("reader123", book)
        print(f"✅ {result}")
        print(f"   📖 Stored: {book['title']} by {book['author']}")
        print(f"   📊 Genre: {book['genre']}, Pages: {book['page_count']}")
        print(f"   ⭐ Rating: {book['rating']}, Year: {book['publication_year']}")
        
        # User decides to add some books to reading list
        if i <= 2:  # Add first 2 books to reading list
            status = "to_read" if i == 1 else "reading"
            readlist_result = add_to_readlist("reader123", book, status)
            print(f"   📋 {readlist_result} (Status: {status})")
    
    # Simulate user viewing the same book again (should update view count)
    print(f"\n--- User views '{books_to_view[0]['title']}' again ---")
    result = add_viewed_book("reader123", books_to_view[0])
    print(f"✅ {result}")
    
    # Show user's viewing history
    print("\n📈 User's Complete Viewing History:")
    viewed_books = get_viewed_books("reader123")
    
    for book in viewed_books:
        print(f"\n📚 {book['title']} by {book['author']}")
        print(f"   📊 Genre: {book['genre']} | Pages: {book['page_count']} | Rating: ⭐{book['rating']}")
        print(f"   👀 Viewed {book['view_count']} time(s) | Last: {book.get('last_viewed_at', book['viewed_at'])}")
        print(f"   📝 Description: {book['description'][:60]}...")
    
    # Show user's reading list with complete details
    print("\n📋 User's Reading List (Complete Details):")
    readlist = get_readlist("reader123")
    
    for item in readlist:
        if item.get('type') == 'book':
            print(f"\n📖 {item['title']} by {item['author']}")
            print(f"   📊 Status: {item['status']} | Genre: {item['genre']}")
            print(f"   📄 Pages: {item['page_count']} | Publisher: {item['publisher']}")
            print(f"   📅 Added: {item['date_added']}")
    
    # Show user profile with reading stats
    print("\n👤 User Profile Summary:")
    profile = get_user_profile("reader123")
    if profile:
        print(f"   Name: {profile['full_name']}")
        print(f"   Email: {profile['email']}")
        print(f"   Reading Goal: {profile['preferences']['reading_goal']} books/year")
        print(f"   Books Read: {profile['profile']['books_read_count']}")
        print(f"   Total Pages: {profile['profile']['total_pages_read']}")
    
    print("\n" + "="*60)
    print("✅ DEMO COMPLETE!")
    print("✅ All book details stored in MongoDB when user viewed books")
    print("✅ Complete book information available for:")
    print("   - Viewing history")
    print("   - Reading lists") 
    print("   - User analytics")
    print("   - Offline access")
    print("="*60)

if __name__ == "__main__":
    simulate_user_book_viewing()
