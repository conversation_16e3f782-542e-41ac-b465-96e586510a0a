from config import get_database
from datetime import datetime

# Get database connection
db = get_database()
if db is None:
    raise Exception("Failed to connect to MongoDB")

viewed_books = db["viewed_books"]

def add_viewed_book(username, book_data):
    """
    Store complete book details when a user views a book

    Args:
        username (str): Username of the viewer
        book_data (dict): Complete book information including:
            - isbn: Book ISBN
            - title: Book title
            - author: Book author
            - cover_url: Book cover image URL
            - description: Book description (optional)
            - genre: Book genre (optional)
            - publication_year: Year published (optional)
            - publisher: Publisher name (optional)
            - page_count: Number of pages (optional)
            - rating: Average rating (optional)
    """

    # Ensure we have required fields
    isbn = book_data.get("isbn")
    if not isbn:
        return "Error: ISBN is required"

    # Check if this book view already exists for this user
    existing_view = viewed_books.find_one({
        "username": username,
        "isbn": isbn
    })

    # Prepare complete book details for storage
    view_record = {
        "username": username,
        "isbn": book_data.get("isbn"),
        "title": book_data.get("title", "Unknown Title"),
        "author": book_data.get("author", "Unknown Author"),
        "cover_url": book_data.get("cover_url"),
        "description": book_data.get("description", ""),
        "genre": book_data.get("genre", ""),
        "publication_year": book_data.get("publication_year"),
        "publisher": book_data.get("publisher", ""),
        "page_count": book_data.get("page_count"),
        "rating": book_data.get("rating"),
        "viewed_at": datetime.now(),
        "view_count": 1
    }

    if existing_view:
        # Update existing view record with new view time and increment count
        viewed_books.update_one(
            {"username": username, "isbn": isbn},
            {
                "$set": {
                    "last_viewed_at": datetime.now(),
                    "title": book_data.get("title", existing_view.get("title")),
                    "author": book_data.get("author", existing_view.get("author")),
                    "cover_url": book_data.get("cover_url", existing_view.get("cover_url")),
                    "description": book_data.get("description", existing_view.get("description", "")),
                    "genre": book_data.get("genre", existing_view.get("genre", "")),
                    "publication_year": book_data.get("publication_year", existing_view.get("publication_year")),
                    "publisher": book_data.get("publisher", existing_view.get("publisher", "")),
                    "page_count": book_data.get("page_count", existing_view.get("page_count")),
                    "rating": book_data.get("rating", existing_view.get("rating"))
                },
                "$inc": {"view_count": 1}
            }
        )
        return "Book view updated"
    else:
        # Insert new view record
        viewed_books.insert_one(view_record)
        return "Book view recorded"

def get_viewed_books(username, limit=None):
    """Get all books viewed by a user with complete details"""
    query = {"username": username}
    cursor = viewed_books.find(query).sort("viewed_at", -1)

    if limit:
        cursor = cursor.limit(limit)

    return list(cursor)

def get_recently_viewed(username, days=30, limit=10):
    """Get recently viewed books within specified days"""
    from datetime import timedelta

    cutoff_date = datetime.now() - timedelta(days=days)
    query = {
        "username": username,
        "viewed_at": {"$gte": cutoff_date}
    }

    return list(viewed_books.find(query).sort("viewed_at", -1).limit(limit))

def get_most_viewed_books(username, limit=10):
    """Get most frequently viewed books by a user"""
    return list(viewed_books.find({"username": username}).sort("view_count", -1).limit(limit))