#!/usr/bin/env python3
"""
Test Real Issue

This demonstrates the actual problem - when you call the functions
with minimal book data (like in real usage), only basic info is stored.
"""

from config import test_connection
from user import register, login
from viewed import add_viewed_book, get_viewed_books
from readlist import add_to_readlist, get_readlist
from clean_database import clean_all_collections

def test_minimal_vs_complete_data():
    """Test the difference between minimal and complete book data storage"""
    
    print("🔍 TESTING REAL ISSUE")
    print("=" * 50)
    
    # Clean database first
    clean_all_collections()
    
    # Test connection
    if not test_connection():
        print("❌ MongoDB connection failed")
        return
    
    # Create user
    register("testuser", "password")
    login("testuser", "password")
    
    print("\n📚 SCENARIO 1: Real usage with minimal book data")
    print("-" * 50)
    
    # This is how you probably call it in real usage - minimal data
    minimal_book = {
        "isbn": "9780123456789",
        "title": "Some Book",
        "author": "Some Author"
        # No genre, pages, rating, description, etc.
    }
    
    print("Calling add_viewed_book with minimal data:")
    print(f"  Book data: {minimal_book}")
    
    result1 = add_viewed_book("testuser", minimal_book)
    print(f"  Result: {result1}")
    
    result2 = add_to_readlist("testuser", minimal_book)
    print(f"  Readlist result: {result2}")
    
    # Check what was actually stored
    viewed = get_viewed_books("testuser")
    readlist = get_readlist("testuser")
    
    print("\n📊 What was stored in viewed_books:")
    if viewed:
        book = viewed[0]
        print(f"  Title: {book.get('title')}")
        print(f"  Author: {book.get('author')}")
        print(f"  Genre: '{book.get('genre')}' (empty!)")
        print(f"  Pages: {book.get('page_count')} (None!)")
        print(f"  Rating: {book.get('rating')} (None!)")
        print(f"  Description: '{book.get('description')}' (empty!)")
    
    print("\n📊 What was stored in readlist:")
    if readlist:
        item = readlist[0]
        print(f"  Title: {item.get('title')}")
        print(f"  Author: {item.get('author')}")
        print(f"  Genre: '{item.get('genre')}' (empty!)")
        print(f"  Pages: {item.get('page_count')} (None!)")
        print(f"  Rating: {item.get('rating')} (None!)")
        print(f"  Description: '{item.get('description')}' (empty!)")
    
    print("\n" + "=" * 50)
    print("❌ PROBLEM IDENTIFIED:")
    print("   When you call functions with minimal book data,")
    print("   only basic info (title, author) gets stored.")
    print("   Genre, pages, rating, etc. are empty/None!")
    print("=" * 50)

if __name__ == "__main__":
    test_minimal_vs_complete_data()
