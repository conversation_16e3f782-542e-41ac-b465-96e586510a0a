#!/usr/bin/env python3
"""
Test Enhanced Fix

This tests that the enhanced functions now automatically
add complete book details even when called with minimal data.
"""

from config import test_connection
from user import register, login
from viewed import add_viewed_book, get_viewed_books
from readlist import add_to_readlist, get_readlist
from clean_database import clean_all_collections

def test_enhanced_functions():
    """Test that functions now auto-enhance minimal book data"""
    
    print("🔧 TESTING ENHANCED FUNCTIONS")
    print("=" * 50)
    
    # Clean database first
    clean_all_collections()
    
    # Test connection
    if not test_connection():
        print("❌ MongoDB connection failed")
        return
    
    # Create user
    register("testuser", "password")
    login("testuser", "password")
    
    print("\n📚 SCENARIO: Real usage with minimal book data")
    print("-" * 50)
    
    # This is how you probably call it in real usage - minimal data
    minimal_book = {
        "isbn": "9780123456789",
        "title": "The Science of Everything",
        "author": "Dr. Smart Person"
        # No genre, pages, rating, description, etc.
    }
    
    print("Calling functions with minimal data:")
    print(f"  Book data: {minimal_book}")
    
    # Test enhanced viewing
    print(f"\n📖 Testing enhanced add_viewed_book...")
    result1 = add_viewed_book("testuser", minimal_book)
    print(f"  Result: {result1}")
    
    # Test enhanced readlist
    print(f"\n📋 Testing enhanced add_to_readlist...")
    result2 = add_to_readlist("testuser", minimal_book)
    print(f"  Result: {result2}")
    
    # Check what was actually stored
    print(f"\n📊 CHECKING ENHANCED STORAGE:")
    print("-" * 30)
    
    viewed = get_viewed_books("testuser")
    readlist = get_readlist("testuser")
    
    print("\n✅ Enhanced viewed_books storage:")
    if viewed:
        book = viewed[0]
        print(f"  📖 Title: {book.get('title')}")
        print(f"  ✍️ Author: {book.get('author')}")
        print(f"  🏷️ Genre: '{book.get('genre')}' (auto-enhanced!)")
        print(f"  📄 Pages: {book.get('page_count')} (auto-enhanced!)")
        print(f"  ⭐ Rating: {book.get('rating')} (auto-enhanced!)")
        print(f"  📝 Description: '{book.get('description')}' (auto-enhanced!)")
        print(f"  🏢 Publisher: '{book.get('publisher')}' (auto-enhanced!)")
        print(f"  📅 Year: {book.get('publication_year')} (auto-enhanced!)")
    
    print("\n✅ Enhanced readlist storage:")
    if readlist:
        item = readlist[0]
        print(f"  📖 Title: {item.get('title')}")
        print(f"  ✍️ Author: {item.get('author')}")
        print(f"  🏷️ Genre: '{item.get('genre')}' (auto-enhanced!)")
        print(f"  📄 Pages: {item.get('page_count')} (auto-enhanced!)")
        print(f"  ⭐ Rating: {item.get('rating')} (auto-enhanced!)")
        print(f"  📝 Description: '{item.get('description')}' (auto-enhanced!)")
        print(f"  🏢 Publisher: '{item.get('publisher')}' (auto-enhanced!)")
        print(f"  📅 Year: {item.get('publication_year')} (auto-enhanced!)")
        print(f"  📊 Status: {item.get('status')}")
    
    print("\n" + "=" * 50)
    
    # Check if enhancement worked
    if viewed and readlist:
        book = viewed[0]
        item = readlist[0]
        
        # Check if we have enhanced data
        has_genre = book.get('genre') and book.get('genre') != ''
        has_pages = book.get('page_count') is not None
        has_rating = book.get('rating') is not None
        has_description = book.get('description') and book.get('description') != ''
        
        if has_genre and has_pages and has_rating and has_description:
            print("✅ SUCCESS: AUTO-ENHANCEMENT WORKING!")
            print("✅ Minimal book data automatically enhanced with:")
            print("   🏷️ Genre (intelligent guess)")
            print("   📄 Page count (default)")
            print("   ⭐ Rating (default)")
            print("   📝 Description (generated)")
            print("   🏢 Publisher (default)")
            print("   📅 Publication year (default)")
        else:
            print("❌ ISSUE: Auto-enhancement not working properly")
    else:
        print("❌ ISSUE: Data not stored properly")
    
    print("=" * 50)

if __name__ == "__main__":
    test_enhanced_functions()
