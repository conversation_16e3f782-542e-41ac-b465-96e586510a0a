from config import get_database
from datetime import datetime

# Get database connection
db = get_database()
if db is None:
    raise Exception("Failed to connect to MongoDB")

viewed_books = db["viewed_books"]

def enhance_book_data(book_data):
    """
    Enhance minimal book data with additional information

    This function takes minimal book data and tries to enhance it with
    additional details from various sources:
    1. Check if book exists in our books collection
    2. Use external APIs (future enhancement)
    3. Apply intelligent defaults
    """
    isbn = book_data.get("isbn")

    # First, try to get complete data from our books collection
    from book import get_book
    existing_book = get_book(isbn) if isbn else None

    # Start with provided data
    enhanced_data = book_data.copy()

    # If we found the book in our collection, use that data
    if existing_book:
        # Merge existing book data, but don't override provided data
        for key, value in existing_book.items():
            if key not in enhanced_data or enhanced_data[key] is None:
                enhanced_data[key] = value

    # Apply intelligent defaults for missing fields
    if not enhanced_data.get("description"):
        enhanced_data["description"] = f"A book by {enhanced_data.get('author', 'Unknown Author')}"

    if not enhanced_data.get("genre"):
        # Try to guess genre from title/description (basic implementation)
        title = enhanced_data.get("title", "").lower()
        if any(word in title for word in ["history", "historical"]):
            enhanced_data["genre"] = "History"
        elif any(word in title for word in ["science", "physics", "chemistry"]):
            enhanced_data["genre"] = "Science"
        elif any(word in title for word in ["novel", "story", "tale"]):
            enhanced_data["genre"] = "Fiction"
        else:
            enhanced_data["genre"] = "General"

    # Set default values for missing numeric fields
    if not enhanced_data.get("page_count"):
        enhanced_data["page_count"] = 250  # Average book length

    if not enhanced_data.get("rating"):
        enhanced_data["rating"] = 4.0  # Default good rating

    if not enhanced_data.get("publication_year"):
        enhanced_data["publication_year"] = 2020  # Recent default

    if not enhanced_data.get("publisher"):
        enhanced_data["publisher"] = "Unknown Publisher"

    return enhanced_data

def add_viewed_book(username, book_data):
    """
    Store complete book details when a user views a book

    Args:
        username (str): Username of the viewer
        book_data (dict): Book information - can be minimal or complete:
            - isbn: Book ISBN (required)
            - title: Book title (required)
            - author: Book author (required)
            - cover_url: Book cover image URL (optional)
            - description: Book description (optional)
            - genre: Book genre (optional)
            - publication_year: Year published (optional)
            - publisher: Publisher name (optional)
            - page_count: Number of pages (optional)
            - rating: Average rating (optional)
    """

    # Ensure we have required fields
    isbn = book_data.get("isbn")
    if not isbn:
        return "Error: ISBN is required"

    # Auto-enhance book data if minimal information provided
    enhanced_book_data = enhance_book_data(book_data)

    # Check if this book view already exists for this user
    existing_view = viewed_books.find_one({
        "username": username,
        "isbn": isbn
    })

    # Prepare complete book details for storage using enhanced data
    view_record = {
        "username": username,
        "isbn": enhanced_book_data.get("isbn"),
        "title": enhanced_book_data.get("title", "Unknown Title"),
        "author": enhanced_book_data.get("author", "Unknown Author"),
        "cover_url": enhanced_book_data.get("cover_url"),
        "description": enhanced_book_data.get("description", ""),
        "genre": enhanced_book_data.get("genre", ""),
        "publication_year": enhanced_book_data.get("publication_year"),
        "publisher": enhanced_book_data.get("publisher", ""),
        "page_count": enhanced_book_data.get("page_count"),
        "rating": enhanced_book_data.get("rating"),
        "viewed_at": datetime.now(),
        "view_count": 1
    }

    if existing_view:
        # Update existing view record with new view time and increment count
        viewed_books.update_one(
            {"username": username, "isbn": isbn},
            {
                "$set": {
                    "last_viewed_at": datetime.now(),
                    "title": enhanced_book_data.get("title", existing_view.get("title")),
                    "author": enhanced_book_data.get("author", existing_view.get("author")),
                    "cover_url": enhanced_book_data.get("cover_url", existing_view.get("cover_url")),
                    "description": enhanced_book_data.get("description", existing_view.get("description", "")),
                    "genre": enhanced_book_data.get("genre", existing_view.get("genre", "")),
                    "publication_year": enhanced_book_data.get("publication_year", existing_view.get("publication_year")),
                    "publisher": enhanced_book_data.get("publisher", existing_view.get("publisher", "")),
                    "page_count": enhanced_book_data.get("page_count", existing_view.get("page_count")),
                    "rating": enhanced_book_data.get("rating", existing_view.get("rating"))
                },
                "$inc": {"view_count": 1}
            }
        )
        return "Book view updated"
    else:
        # Insert new view record
        viewed_books.insert_one(view_record)
        return "Book view recorded"

def get_viewed_books(username, limit=None):
    """Get all books viewed by a user with complete details"""
    query = {"username": username}
    cursor = viewed_books.find(query).sort("viewed_at", -1)

    if limit:
        cursor = cursor.limit(limit)

    return list(cursor)

def get_recently_viewed(username, days=30, limit=10):
    """Get recently viewed books within specified days"""
    from datetime import timedelta

    cutoff_date = datetime.now() - timedelta(days=days)
    query = {
        "username": username,
        "viewed_at": {"$gte": cutoff_date}
    }

    return list(viewed_books.find(query).sort("viewed_at", -1).limit(limit))

def get_most_viewed_books(username, limit=10):
    """Get most frequently viewed books by a user"""
    return list(viewed_books.find({"username": username}).sort("view_count", -1).limit(limit))