#!/usr/bin/env python3
"""
Demo Real Usage - FIXED

This demonstrates how the enhanced functions now automatically
store complete book details even when called with minimal data.
"""

from config import test_connection
from user import register, login
from viewed import add_viewed_book, get_viewed_books
from readlist import add_to_readlist, get_readlist
from clean_database import clean_all_collections

def demonstrate_fixed_usage():
    """Demonstrate that minimal book data now gets auto-enhanced"""
    
    print("🎯 REAL USAGE DEMO - PROBLEM FIXED!")
    print("=" * 60)
    print("Now when you call functions with minimal book data,")
    print("complete details are automatically stored!")
    print("=" * 60)
    
    # Clean database
    clean_all_collections()
    
    # Test connection
    if not test_connection():
        print("❌ MongoDB connection failed")
        return
    
    # Create user
    print("\n👤 Creating user...")
    register("realuser", "password123")
    login("realuser", "password123")
    
    print("\n📚 REAL SCENARIO: User discovers books")
    print("-" * 40)
    
    # These are the kind of minimal book data you probably have in real usage
    real_books = [
        {
            "isbn": "9781234567890",
            "title": "The History of Ancient Rome",
            "author": "Marcus Historian"
        },
        {
            "isbn": "9780987654321", 
            "title": "Modern Physics Explained",
            "author": "Dr. <PERSON> Jr"
        },
        {
            "isbn": "9781122334455",
            "title": "The Great Adventure Novel",
            "author": "Jane Storyteller"
        }
    ]
    
    for i, book in enumerate(real_books, 1):
        print(f"\n📖 Book {i}: User views '{book['title']}'")
        print(f"   Input data: {book}")
        
        # User views book with minimal data
        view_result = add_viewed_book("realuser", book)
        print(f"   ✅ {view_result}")
        
        # User adds to readlist with minimal data
        if i <= 2:  # Add first 2 to readlist
            status = "to_read" if i == 1 else "reading"
            readlist_result = add_to_readlist("realuser", book, status)
            print(f"   📋 {readlist_result} (Status: {status})")
    
    # Show what was actually stored
    print(f"\n📊 WHAT WAS ACTUALLY STORED:")
    print("=" * 40)
    
    print(f"\n👀 Viewing History (Auto-Enhanced):")
    viewed_books = get_viewed_books("realuser")
    
    for book in viewed_books:
        print(f"\n📚 {book['title']}")
        print(f"   ✍️ Author: {book['author']}")
        print(f"   🏷️ Genre: {book['genre']} (auto-detected!)")
        print(f"   📄 Pages: {book['page_count']} (default)")
        print(f"   ⭐ Rating: {book['rating']} (default)")
        print(f"   📝 Description: {book['description']}")
        print(f"   🏢 Publisher: {book['publisher']}")
        print(f"   📅 Year: {book['publication_year']}")
    
    print(f"\n📋 Reading List (Auto-Enhanced):")
    readlist = get_readlist("realuser")
    
    for item in readlist:
        if item.get('type') == 'book':
            print(f"\n📖 {item['title']}")
            print(f"   📊 Status: {item['status']}")
            print(f"   🏷️ Genre: {item['genre']} (auto-detected!)")
            print(f"   📄 Pages: {item['page_count']} (default)")
            print(f"   ⭐ Rating: {item['rating']} (default)")
            print(f"   📅 Added: {item['date_added'].strftime('%Y-%m-%d %H:%M')}")
    
    print(f"\n" + "=" * 60)
    print("✅ PROBLEM SOLVED!")
    print("✅ Now when you call:")
    print("   add_viewed_book(username, minimal_book_data)")
    print("   add_to_readlist(username, minimal_book_data)")
    print("")
    print("✅ Complete book details are automatically stored:")
    print("   🏷️ Genre (intelligent detection)")
    print("   📄 Page count (reasonable default)")
    print("   ⭐ Rating (default 4.0)")
    print("   📝 Description (auto-generated)")
    print("   🏢 Publisher (default)")
    print("   📅 Publication year (default)")
    print("")
    print("✅ No more empty/None values!")
    print("✅ Rich book data available for analytics!")
    print("=" * 60)

if __name__ == "__main__":
    demonstrate_fixed_usage()
