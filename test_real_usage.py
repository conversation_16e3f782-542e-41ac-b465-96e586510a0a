#!/usr/bin/env python3
"""
Test Real Usage - No Test Cases

This script tests the actual viewing and readlist functionality
without any test cases, to ensure real usage works properly.
"""

from config import test_connection
from user import register, login
from viewed import add_viewed_book, get_viewed_books
from readlist import add_to_readlist, get_readlist
from book import add_book

def test_real_book_viewing():
    """Test real book viewing and readlist functionality"""
    
    print("🧪 TESTING REAL USAGE (NO TEST CASES)")
    print("=" * 50)
    
    # Test connection
    if not test_connection():
        print("❌ MongoDB connection failed")
        return
    
    # Create a real user
    print("\n1. Creating real user...")
    register("real_user", "password123", email="<EMAIL>", full_name="Real User")
    login("real_user", "password123")
    print("✅ User created and logged in")
    
    # Create a real book with complete details
    print("\n2. Adding a real book to catalog...")
    book_details = {
        "isbn": "9780316769174",
        "title": "The Catcher in the Rye",
        "author": "<PERSON><PERSON><PERSON><PERSON>",
        "cover_url": "https://example.com/catcher-cover.jpg",
        "description": "A classic coming-of-age story about Holden <PERSON>aulfield.",
        "genre": "Classic Literature",
        "publication_year": 1951,
        "publisher": "Little, Brown and Company",
        "page_count": 277,
        "rating": 3.8
    }
    
    # Add book to catalog
    add_book(**book_details)
    print("✅ Book added to catalog")
    
    # Test real book viewing
    print("\n3. User views the book (should store complete details)...")
    view_result = add_viewed_book("real_user", book_details)
    print(f"   Result: {view_result}")
    
    # Check if viewing was stored properly
    print("\n4. Checking viewed books storage...")
    viewed_books = get_viewed_books("real_user")
    print(f"   Number of viewed books: {len(viewed_books)}")
    
    if viewed_books:
        book = viewed_books[0]
        print(f"   ✅ Book stored: {book.get('title')} by {book.get('author')}")
        print(f"   ✅ Genre: {book.get('genre')}")
        print(f"   ✅ Pages: {book.get('page_count')}")
        print(f"   ✅ Rating: {book.get('rating')}")
        print(f"   ✅ Description: {book.get('description')[:50]}...")
    else:
        print("   ❌ No viewed books found!")
    
    # Test real readlist addition
    print("\n5. User adds book to readlist (should store complete details)...")
    readlist_result = add_to_readlist("real_user", book_details, "to_read")
    print(f"   Result: {readlist_result}")
    
    # Check if readlist was stored properly
    print("\n6. Checking readlist storage...")
    readlist = get_readlist("real_user")
    print(f"   Number of readlist items: {len(readlist)}")
    
    if readlist:
        item = readlist[0]
        if item.get('type') == 'book':
            print(f"   ✅ Book stored: {item.get('title')} by {item.get('author')}")
            print(f"   ✅ Genre: {item.get('genre')}")
            print(f"   ✅ Pages: {item.get('page_count')}")
            print(f"   ✅ Rating: {item.get('rating')}")
            print(f"   ✅ Status: {item.get('status')}")
            print(f"   ✅ Description: {item.get('description')[:50]}...")
        else:
            print("   ❌ Item is not a book!")
    else:
        print("   ❌ No readlist items found!")
    
    # Test viewing the same book again
    print("\n7. User views the same book again (should update view count)...")
    view_result2 = add_viewed_book("real_user", book_details)
    print(f"   Result: {view_result2}")
    
    # Check updated view count
    viewed_books_updated = get_viewed_books("real_user")
    if viewed_books_updated:
        book = viewed_books_updated[0]
        print(f"   ✅ View count: {book.get('view_count')}")
    
    print("\n" + "=" * 50)
    print("🎯 REAL USAGE TEST COMPLETE")
    
    # Summary
    if viewed_books and readlist:
        print("✅ SUCCESS: Real viewing and readlist functions work properly!")
        print("✅ Complete book details are being stored correctly!")
    else:
        print("❌ ISSUE: Real functions are not storing data properly!")
    
    print("=" * 50)

if __name__ == "__main__":
    test_real_book_viewing()
