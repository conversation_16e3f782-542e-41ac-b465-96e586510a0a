#!/usr/bin/env python3
"""
MongoDB Django Integration

This module provides integration between Django authentication
and MongoDB storage for user profiles, books, and reading data.
"""

from config import get_database
from datetime import datetime
import hashlib

# Get MongoDB database
db = get_database()
if db is None:
    raise Exception("Failed to connect to MongoDB")

# MongoDB collections
users_collection = db["django_users"]
books_collection = db["books"]
readlist_collection = db["readlist"]
viewed_books_collection = db["viewed_books"]

class MongoDBUserManager:
    """Manage user data in MongoDB alongside Django authentication"""
    
    @staticmethod
    def create_user_profile(django_user):
        """
        Create a MongoDB user profile when Django user is created
        
        Args:
            django_user: Django User object
        """
        user_profile = {
            "django_user_id": django_user.id,
            "username": django_user.username,
            "email": django_user.email,
            "first_name": django_user.first_name,
            "last_name": django_user.last_name,
            "date_joined": django_user.date_joined,
            "last_login": django_user.last_login,
            "is_active": django_user.is_active,
            "profile": {
                "reading_goal": 0,
                "favorite_genres": [],
                "favorite_authors": [],
                "books_read_count": 0,
                "total_pages_read": 0,
                "bio": "",
                "location": ""
            },
            "preferences": {
                "email_notifications": True,
                "reading_reminders": False,
                "privacy_level": "public"
            },
            "created_at": datetime.now(),
            "updated_at": datetime.now()
        }
        
        # Check if profile already exists
        existing = users_collection.find_one({"django_user_id": django_user.id})
        if not existing:
            users_collection.insert_one(user_profile)
            return "MongoDB user profile created"
        else:
            return "User profile already exists"
    
    @staticmethod
    def get_user_profile(django_user):
        """Get MongoDB user profile by Django user"""
        return users_collection.find_one({"django_user_id": django_user.id})
    
    @staticmethod
    def update_user_profile(django_user, updates):
        """Update MongoDB user profile"""
        updates["updated_at"] = datetime.now()
        
        result = users_collection.update_one(
            {"django_user_id": django_user.id},
            {"$set": updates}
        )
        
        if result.matched_count == 0:
            # Create profile if it doesn't exist
            MongoDBUserManager.create_user_profile(django_user)
            return MongoDBUserManager.update_user_profile(django_user, updates)
        
        return "Profile updated successfully"
    
    @staticmethod
    def sync_django_user(django_user):
        """Sync Django user data with MongoDB profile"""
        updates = {
            "username": django_user.username,
            "email": django_user.email,
            "first_name": django_user.first_name,
            "last_name": django_user.last_name,
            "last_login": django_user.last_login,
            "is_active": django_user.is_active,
            "updated_at": datetime.now()
        }
        
        users_collection.update_one(
            {"django_user_id": django_user.id},
            {"$set": updates},
            upsert=True
        )

class MongoDBBookManager:
    """Manage book data in MongoDB for Django users"""
    
    @staticmethod
    def add_viewed_book(django_user, book_data):
        """Add viewed book for Django user"""
        from viewed import enhance_book_data
        
        enhanced_book_data = enhance_book_data(book_data)
        isbn = enhanced_book_data.get("isbn")
        
        # Check if already viewed
        existing = viewed_books_collection.find_one({
            "django_user_id": django_user.id,
            "isbn": isbn
        })
        
        if existing:
            # Update view count and last viewed
            viewed_books_collection.update_one(
                {"django_user_id": django_user.id, "isbn": isbn},
                {
                    "$set": {
                        "last_viewed_at": datetime.now(),
                        **enhanced_book_data
                    },
                    "$inc": {"view_count": 1}
                }
            )
            return "Book view updated"
        else:
            # Create new view record
            view_record = {
                "django_user_id": django_user.id,
                "username": django_user.username,
                "viewed_at": datetime.now(),
                "view_count": 1,
                **enhanced_book_data
            }
            viewed_books_collection.insert_one(view_record)
            return "Book view recorded"
    
    @staticmethod
    def add_to_readlist(django_user, book_data, status="to_read"):
        """Add book to Django user's reading list"""
        from viewed import enhance_book_data
        
        enhanced_book_data = enhance_book_data(book_data)
        isbn = enhanced_book_data.get("isbn")
        
        # Check if already in readlist
        existing = readlist_collection.find_one({
            "django_user_id": django_user.id,
            "isbn": isbn
        })
        
        if existing:
            return "Book already in readlist"
        
        # Create readlist entry
        readlist_entry = {
            "django_user_id": django_user.id,
            "username": django_user.username,
            "status": status,
            "date_added": datetime.now(),
            "type": "book",
            **enhanced_book_data
        }
        
        readlist_collection.insert_one(readlist_entry)
        return "Book added to readlist"
    
    @staticmethod
    def get_user_readlist(django_user):
        """Get Django user's reading list"""
        return list(readlist_collection.find({"django_user_id": django_user.id}))
    
    @staticmethod
    def get_user_viewed_books(django_user):
        """Get Django user's viewed books"""
        return list(viewed_books_collection.find({"django_user_id": django_user.id}).sort("viewed_at", -1))
    
    @staticmethod
    def update_reading_status(django_user, isbn, new_status):
        """Update reading status for Django user"""
        result = readlist_collection.update_one(
            {"django_user_id": django_user.id, "isbn": isbn},
            {"$set": {"status": new_status, "date_updated": datetime.now()}}
        )
        
        if result.matched_count == 0:
            return "Book not found in readlist"
        return f"Status updated to {new_status}"

def sync_all_django_users():
    """Sync all Django users with MongoDB"""
    try:
        from django.contrib.auth.models import User
        
        synced_count = 0
        for django_user in User.objects.all():
            MongoDBUserManager.create_user_profile(django_user)
            MongoDBUserManager.sync_django_user(django_user)
            synced_count += 1
        
        return f"Synced {synced_count} Django users with MongoDB"
    
    except ImportError:
        return "Django not available - run this from Django environment"

if __name__ == "__main__":
    print("MongoDB Django Integration Module")
    print("Use this module to integrate Django authentication with MongoDB storage")
