from config import get_database
from datetime import datetime

# Get database connection
db = get_database()
if db is None:
    raise Exception("Failed to connect to MongoDB")

books = db["books"]

def add_book(isbn, title, author, cover_url=None, description=None, genre=None,
             publication_year=None, publisher=None, page_count=None, rating=None):
    """
    Add a book with complete details to the database

    Args:
        isbn (str): Book ISBN
        title (str): Book title
        author (str): Book author
        cover_url (str, optional): Book cover image URL
        description (str, optional): Book description
        genre (str, optional): Book genre
        publication_year (int, optional): Year published
        publisher (str, optional): Publisher name
        page_count (int, optional): Number of pages
        rating (float, optional): Average rating
    """
    if books.find_one({"isbn": isbn}):
        return "Book already exists"

    book_data = {
        "isbn": isbn,
        "title": title,
        "author": author,
        "cover_url": cover_url,
        "description": description or "",
        "genre": genre or "",
        "publication_year": publication_year,
        "publisher": publisher or "",
        "page_count": page_count,
        "rating": rating,
        "date_added": datetime.now()
    }

    books.insert_one(book_data)
    return "Book added"

def get_book(isbn):
    return books.find_one({"isbn": isbn})

def get_all_books():
    return list(books.find())

def delete_book(isbn):
    result = books.delete_one({"isbn": isbn})
    if result.deleted_count == 0:
        return "Book not found"
    return "Book deleted"

def update_book(isbn, title=None, author=None, cover_url=None):
    update_fields = {}
    if title:
        update_fields["title"] = title
    if author:
        update_fields["author"] = author
    if cover_url:
        update_fields["cover_url"] = cover_url
    result = books.update_one({"isbn": isbn}, {"$set": update_fields})
    if result.matched_count == 0:
        return "Book not found"
    return "Book updated"