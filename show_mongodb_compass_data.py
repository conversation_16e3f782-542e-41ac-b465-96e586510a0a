#!/usr/bin/env python3
"""
Show MongoDB Compass Data

This script shows exactly what data is in MongoDB
and where to find it in MongoDB Compass.
"""

from config import get_database

def show_compass_data():
    """Show exactly what's in MongoDB for Compass viewing"""
    
    print("🔍 MONGODB COMPASS DATA LOCATION")
    print("=" * 50)
    
    db = get_database()
    if not db:
        print("❌ Cannot connect to MongoDB")
        return
    
    print("📍 IN MONGODB COMPASS:")
    print("   1. Connect to: mongodb://localhost:27017")
    print("   2. Look for database: 'readinglist_app'")
    print("   3. Click on 'readinglist_app' to expand")
    print("   4. You'll see these collections:")
    
    collections = ["django_users", "readlist", "viewed_books", "books"]
    
    for collection_name in collections:
        collection = db[collection_name]
        count = collection.count_documents({})
        print(f"\n📁 {collection_name}: {count} documents")
        
        if count > 0:
            print("   Sample data:")
            # Show first document
            sample = collection.find_one()
            if collection_name == "django_users":
                print(f"   👤 Username: {sample.get('username')}")
                print(f"   📧 Email: {sample.get('email')}")
                print(f"   🆔 Django ID: {sample.get('django_user_id')}")
                print(f"   📅 Created: {sample.get('created_at')}")
            elif collection_name in ["readlist", "viewed_books"]:
                print(f"   📚 Title: {sample.get('title')}")
                print(f"   ✍️ Author: {sample.get('author')}")
                print(f"   🏷️ Genre: {sample.get('genre')}")
                if collection_name == "readlist":
                    print(f"   📊 Status: {sample.get('status')}")
                if collection_name == "viewed_books":
                    print(f"   👀 View count: {sample.get('view_count')}")
    
    print(f"\n🎯 TO SEE YOUR DJANGO USER DATA:")
    print("=" * 40)
    print("1. In MongoDB Compass, click 'readinglist_app'")
    print("2. Click 'django_users' collection")
    print("3. You'll see your Django user 'naveen' with:")
    print("   - Username: naveen")
    print("   - Email: <EMAIL>")
    print("   - Django User ID: 1")
    print("   - Profile data with reading preferences")
    
    print(f"\n📚 TO SEE BOOK DATA:")
    print("=" * 40)
    print("1. Click 'readlist' collection → Reading list items")
    print("2. Click 'viewed_books' collection → Book viewing history")
    print("3. Each document contains complete book details")
    
    print(f"\n⚠️ IMPORTANT:")
    print("You were looking at 'auth_user' database")
    print("But Django-MongoDB integration uses 'readinglist_app' database")
    print("That's why you couldn't see the data!")

if __name__ == "__main__":
    show_compass_data()
