#!/usr/bin/env python3
"""
Sync Django Users with MongoDB

This script syncs existing Django users with MongoDB
and demonstrates the integration.
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'bookrecommender.settings')
django.setup()

from django.contrib.auth.models import User
from mongodb_django_integration import MongoDBUserManager, MongoDBBookManager
from config import test_connection, get_database

def sync_django_users_with_mongodb():
    """Sync all Django users with MongoDB"""
    
    print("🔗 SYNCING DJANGO USERS WITH MONGODB")
    print("=" * 50)
    
    # Test MongoDB connection
    print("\n📡 Testing MongoDB connection...")
    if not test_connection():
        print("❌ MongoDB connection failed!")
        return False
    
    print("✅ MongoDB connection successful!")
    
    # Get Django users
    django_users = User.objects.all()
    print(f"\n👥 Found {django_users.count()} Django users:")
    
    for user in django_users:
        print(f"  📱 {user.username} ({user.email}) - Joined: {user.date_joined}")
    
    # Sync each user with MongoDB
    print(f"\n🔄 Syncing users with MongoDB...")
    synced_count = 0
    
    for user in django_users:
        try:
            # Create MongoDB profile
            result = MongoDBUserManager.create_user_profile(user)
            print(f"  ✅ {user.username}: {result}")
            
            # Sync user data
            MongoDBUserManager.sync_django_user(user)
            synced_count += 1
            
        except Exception as e:
            print(f"  ❌ Error syncing {user.username}: {e}")
    
    print(f"\n📊 Sync Summary:")
    print(f"  ✅ Successfully synced: {synced_count} users")
    print(f"  📱 Total Django users: {django_users.count()}")
    
    return True

def test_mongodb_integration():
    """Test the MongoDB integration with a Django user"""
    
    print(f"\n🧪 TESTING MONGODB INTEGRATION")
    print("=" * 40)
    
    # Get first Django user
    user = User.objects.first()
    if not user:
        print("❌ No Django users found!")
        return
    
    print(f"Testing with user: {user.username}")
    
    # Test book viewing
    test_book = {
        "isbn": "9781234567890",
        "title": "Django MongoDB Integration Test",
        "author": "Test Author"
    }
    
    print(f"\n📖 Testing book viewing...")
    view_result = MongoDBBookManager.add_viewed_book(user, test_book)
    print(f"  Result: {view_result}")
    
    # Test adding to readlist
    print(f"\n📋 Testing add to readlist...")
    readlist_result = MongoDBBookManager.add_to_readlist(user, test_book, "to_read")
    print(f"  Result: {readlist_result}")
    
    # Get user's MongoDB data
    print(f"\n📊 Getting user's MongoDB data...")
    
    # User profile
    profile = MongoDBUserManager.get_user_profile(user)
    if profile:
        print(f"  ✅ MongoDB profile exists")
        print(f"     📅 Created: {profile.get('created_at')}")
        print(f"     📧 Email: {profile.get('email')}")
    
    # Viewed books
    viewed_books = MongoDBBookManager.get_user_viewed_books(user)
    print(f"  📖 Viewed books: {len(viewed_books)}")
    for book in viewed_books:
        print(f"     - {book.get('title')} (Genre: {book.get('genre')})")
    
    # Reading list
    readlist = MongoDBBookManager.get_user_readlist(user)
    print(f"  📋 Reading list: {len(readlist)}")
    for item in readlist:
        print(f"     - {item.get('title')} (Status: {item.get('status')})")

def show_mongodb_collections():
    """Show what's in MongoDB collections"""
    
    print(f"\n📊 MONGODB COLLECTIONS STATUS")
    print("=" * 40)
    
    db = get_database()
    if not db:
        print("❌ Cannot connect to MongoDB")
        return
    
    collections = ["django_users", "books", "readlist", "viewed_books"]
    
    for collection_name in collections:
        collection = db[collection_name]
        count = collection.count_documents({})
        print(f"📁 {collection_name}: {count} documents")
        
        if count > 0:
            # Show sample documents
            sample = list(collection.find().limit(2))
            for doc in sample:
                if collection_name == "django_users":
                    print(f"   👤 {doc.get('username')} (Django ID: {doc.get('django_user_id')})")
                elif collection_name in ["books", "viewed_books", "readlist"]:
                    print(f"   📚 {doc.get('title')} by {doc.get('author')}")

if __name__ == "__main__":
    # Sync Django users with MongoDB
    success = sync_django_users_with_mongodb()
    
    if success:
        # Test the integration
        test_mongodb_integration()
        
        # Show MongoDB status
        show_mongodb_collections()
        
        print(f"\n🎉 SUCCESS!")
        print("✅ Django users are now synced with MongoDB")
        print("✅ Book viewing and readlist functions work")
        print("✅ Check MongoDB Compass to see the data")
        print(f"\n📱 Next steps:")
        print("1. Run Django server: python manage.py runserver")
        print("2. Register/login users in the web app")
        print("3. View books and add to readlist")
        print("4. Check MongoDB Compass for real-time data")
    else:
        print("❌ Sync failed - check MongoDB connection")
