from config import get_database
from datetime import datetime

# Get database connection
db = get_database()
if db is None:
    raise Exception("Failed to connect to MongoDB")

readlist = db["readlist"]

def add_to_readlist(username, book_data, status="to_read"):
    """
    Add a book with complete details to user's reading list

    Args:
        username (str): Username
        book_data (dict or str): Either complete book data dict or just ISBN string
        status (str): Reading status ('to_read', 'reading', 'completed')
    """
    # Handle both book_data dict and simple ISBN string for backward compatibility
    if isinstance(book_data, str):
        isbn = book_data
        # Try to get book details from books collection
        from book import get_book
        book_details = get_book(isbn)
        if book_details:
            book_data = book_details
        else:
            book_data = {"isbn": isbn, "title": "Unknown Title", "author": "Unknown Author"}
    else:
        isbn = book_data.get("isbn")

    if not isbn:
        return "Error: ISBN is required"

    existing = readlist.find_one({"username": username, "isbn": isbn})
    if existing:
        return "Book already in readlist"

    # Auto-enhance book data if minimal information provided
    from viewed import enhance_book_data
    enhanced_book_data = enhance_book_data(book_data)

    # Store complete book details in readlist
    readlist_entry = {
        "username": username,
        "isbn": isbn,
        "title": enhanced_book_data.get("title", "Unknown Title"),
        "author": enhanced_book_data.get("author", "Unknown Author"),
        "cover_url": enhanced_book_data.get("cover_url"),
        "description": enhanced_book_data.get("description", ""),
        "genre": enhanced_book_data.get("genre", ""),
        "publication_year": enhanced_book_data.get("publication_year"),
        "publisher": enhanced_book_data.get("publisher", ""),
        "page_count": enhanced_book_data.get("page_count"),
        "rating": enhanced_book_data.get("rating"),
        "status": status,
        "date_added": datetime.now(),
        "type": "book"
    }

    readlist.insert_one(readlist_entry)
    return "Book added to readlist"

def get_readlist(username):
    """Get user's reading list"""
    return list(readlist.find({"username": username}))

def update_status(username, isbn, new_status):
    """Update the status of a book in user's reading list"""
    result = readlist.update_one(
        {"username": username, "isbn": isbn},
        {"$set": {"status": new_status, "date_updated": datetime.now()}}
    )
    if result.matched_count == 0:
        return "Book not found in readlist"
    return f"Status updated to {new_status}"

def remove_from_readlist(username, isbn):
    """Remove a book from user's reading list"""
    result = readlist.delete_one({"username": username, "isbn": isbn})
    if result.deleted_count == 0:
        return "Book not found in readlist"
    return "Book removed from readlist"

def add_movie_to_readlist(username, movie_title, director, status="to_watch"):
    """Add a movie to user's reading list"""
    readlist.insert_one({
        "username": username,
        "movie_title": movie_title,
        "director": director,
        "status": status,
        "date_added": datetime.now(),
        "type": "movie"
    })
    return "Movie added to readlist"