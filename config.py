"""
MongoDB Configuration Module

This module contains all MongoDB connection settings and helper functions
to ensure consistent database connections across the application.
"""

from pymongo import MongoClient
from pymongo.errors import ConnectionFailure, ServerSelectionTimeoutError

# MongoDB Configuration
MONGODB_URI = "mongodb://localhost:27017/"
DATABASE_NAME = "readinglist_app"

# Connection timeout settings (in milliseconds)
SERVER_SELECTION_TIMEOUT = 5000   # 5 seconds
CONNECTION_TIMEOUT = 10000        # 10 seconds  
SOCKET_TIMEOUT = 20000           # 20 seconds

def get_mongodb_client():
    """
    Create and return a MongoDB client with proper configuration
    
    Returns:
        MongoClient: Configured MongoDB client or None if connection fails
    """
    try:
        client = MongoClient(
            MONGODB_URI,
            serverSelectionTimeoutMS=SERVER_SELECTION_TIMEOUT,
            connectTimeoutMS=CONNECTION_TIMEOUT,
            socketTimeoutMS=SOCKET_TIMEOUT
        )
        
        # Test the connection
        client.admin.command('ping')
        return client
        
    except (ConnectionFailure, ServerSelectionTimeoutError) as e:
        print(f"❌ MongoDB connection error: {e}")
        return None
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return None

def get_database():
    """
    Get the main application database

    Returns:
        Database: MongoDB database object or None if connection fails
    """
    client = get_mongodb_client()
    if client:
        return client[DATABASE_NAME]
    return None

def get_collections():
    """
    Get all main collections used by the application
    
    Returns:
        tuple: (users, books, readlist, viewed_books) collections or (None, None, None, None)
    """
    db = get_database()
    if db:
        return (
            db["users"],
            db["books"], 
            db["readlist"],
            db["viewed_books"]
        )
    return None, None, None, None

def test_connection():
    """
    Test MongoDB connection and print status

    Returns:
        bool: True if connection successful, False otherwise
    """
    client = get_mongodb_client()
    if client:
        try:
            # Get server info to verify connection
            server_info = client.server_info()
            print("✅ Successfully connected to MongoDB!")
            print(f"   Server version: {server_info.get('version', 'Unknown')}")
            print(f"   Database: {DATABASE_NAME}")
            return True
        except Exception as e:
            print(f"❌ Connection test failed: {e}")
            return False
    else:
        print("❌ Failed to establish MongoDB connection")
        print("   Make sure MongoDB is running on localhost:27017")
        return False

if __name__ == "__main__":
    # Test the connection when running this file directly
    print("Testing MongoDB connection...")
    test_connection()
