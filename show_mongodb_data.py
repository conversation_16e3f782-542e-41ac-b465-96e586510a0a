#!/usr/bin/env python3
"""
MongoDB Data Viewer

This script shows all the data currently stored in MongoDB
to demonstrate the enhanced storage system.
"""

from config import get_database
from datetime import datetime

def show_all_data():
    """Display all data stored in MongoDB collections"""
    
    print("=" * 80)
    print("MONGODB DATA VIEWER - Enhanced Storage System")
    print("=" * 80)
    
    # Get database
    db = get_database()
    if not db:
        print("❌ Failed to connect to MongoDB")
        return
    
    # Get all collections
    collections = {
        "users": db["users"],
        "books": db["books"],
        "viewed_books": db["viewed_books"],
        "readlist": db["readlist"]
    }
    
    for collection_name, collection in collections.items():
        print(f"\n📊 COLLECTION: {collection_name.upper()}")
        print("-" * 60)
        
        # Get count
        count = collection.count_documents({})
        print(f"Total documents: {count}")
        
        if count == 0:
            print("   (No documents found)")
            continue
        
        # Show all documents
        documents = list(collection.find())
        
        for i, doc in enumerate(documents, 1):
            print(f"\n📄 Document {i}:")
            
            # Format document display based on collection type
            if collection_name == "users":
                print(f"   👤 Username: {doc.get('username')}")
                print(f"   📧 Email: {doc.get('email', 'Not provided')}")
                print(f"   👨‍💼 Full Name: {doc.get('full_name', 'Not provided')}")
                print(f"   📅 Registered: {doc.get('date_registered')}")
                print(f"   🕐 Last Login: {doc.get('last_login', 'Never')}")
                
                profile = doc.get('profile', {})
                print(f"   📚 Books Read: {profile.get('books_read_count', 0)}")
                print(f"   📄 Pages Read: {profile.get('total_pages_read', 0)}")
                print(f"   🎯 Reading Goal: {doc.get('preferences', {}).get('reading_goal', 'Not set')}")
                
                if profile.get('favorite_authors'):
                    print(f"   ❤️ Favorite Authors: <AUTHORS>
                
            elif collection_name == "books":
                print(f"   📖 Title: {doc.get('title')}")
                print(f"   ✍️ Author: {doc.get('author')}")
                print(f"   🔢 ISBN: {doc.get('isbn')}")
                print(f"   🏷️ Genre: {doc.get('genre', 'Not specified')}")
                print(f"   📅 Year: {doc.get('publication_year', 'Unknown')}")
                print(f"   📄 Pages: {doc.get('page_count', 'Unknown')}")
                print(f"   ⭐ Rating: {doc.get('rating', 'Not rated')}")
                print(f"   🏢 Publisher: {doc.get('publisher', 'Unknown')}")
                if doc.get('description'):
                    print(f"   📝 Description: {doc.get('description')[:100]}...")
                
            elif collection_name == "viewed_books":
                print(f"   👤 User: {doc.get('username')}")
                print(f"   📖 Book: {doc.get('title')} by {doc.get('author')}")
                print(f"   🔢 ISBN: {doc.get('isbn')}")
                print(f"   🏷️ Genre: {doc.get('genre', 'Not specified')}")
                print(f"   📄 Pages: {doc.get('page_count', 'Unknown')}")
                print(f"   ⭐ Rating: {doc.get('rating', 'Not rated')}")
                print(f"   👀 View Count: {doc.get('view_count', 1)}")
                print(f"   🕐 First Viewed: {doc.get('viewed_at')}")
                if doc.get('last_viewed_at'):
                    print(f"   🕐 Last Viewed: {doc.get('last_viewed_at')}")
                
            elif collection_name == "readlist":
                print(f"   👤 User: {doc.get('username')}")
                
                if doc.get('type') == 'movie':
                    print(f"   🎬 Movie: {doc.get('movie_title')}")
                    print(f"   🎭 Director: {doc.get('director')}")
                    print(f"   📊 Status: {doc.get('status')}")
                else:
                    print(f"   📖 Book: {doc.get('title')} by {doc.get('author')}")
                    print(f"   🔢 ISBN: {doc.get('isbn')}")
                    print(f"   🏷️ Genre: {doc.get('genre', 'Not specified')}")
                    print(f"   📄 Pages: {doc.get('page_count', 'Unknown')}")
                    print(f"   ⭐ Rating: {doc.get('rating', 'Not rated')}")
                    print(f"   📊 Status: {doc.get('status')}")
                
                print(f"   📅 Added: {doc.get('date_added')}")
                if doc.get('date_updated'):
                    print(f"   🔄 Updated: {doc.get('date_updated')}")
    
    # Summary statistics
    print(f"\n📈 SUMMARY STATISTICS")
    print("-" * 60)
    
    total_users = collections["users"].count_documents({})
    total_books = collections["books"].count_documents({})
    total_views = collections["viewed_books"].count_documents({})
    total_readlist_items = collections["readlist"].count_documents({})
    
    print(f"👥 Total Users: {total_users}")
    print(f"📚 Total Books in Catalog: {total_books}")
    print(f"👀 Total Book Views: {total_views}")
    print(f"📋 Total Reading List Items: {total_readlist_items}")
    
    # Most viewed books
    if total_views > 0:
        print(f"\n🔥 Most Viewed Books:")
        most_viewed = list(collections["viewed_books"].find().sort("view_count", -1).limit(3))
        for book in most_viewed:
            print(f"   📖 {book.get('title')} - {book.get('view_count')} views")
    
    # Reading status distribution
    if total_readlist_items > 0:
        print(f"\n📊 Reading Status Distribution:")
        pipeline = [
            {"$match": {"type": {"$ne": "movie"}}},
            {"$group": {"_id": "$status", "count": {"$sum": 1}}}
        ]
        status_counts = list(collections["readlist"].aggregate(pipeline))
        for status in status_counts:
            print(f"   📚 {status['_id']}: {status['count']} books")
    
    print("\n" + "=" * 80)
    print("✅ DATA DISPLAY COMPLETE")
    print("✅ Enhanced MongoDB storage system is working perfectly!")
    print("✅ Complete book details stored across all collections")
    print("=" * 80)

if __name__ == "__main__":
    show_all_data()
