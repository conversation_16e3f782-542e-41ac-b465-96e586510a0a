#!/usr/bin/env python3
"""
Clean Usage Demo

This demonstrates the enhanced MongoDB storage system
with real user interactions - no test cases.
"""

from config import test_connection
from user import register, login, get_user_profile
from book import add_book
from viewed import add_viewed_book, get_viewed_books
from readlist import add_to_readlist, get_readlist, update_status

def demonstrate_real_usage():
    """Demonstrate real user interactions with complete book details storage"""
    
    print("📚 REAL BOOK TRACKING SYSTEM DEMO")
    print("=" * 60)
    print("Demonstrating complete book details storage when:")
    print("  ✓ Users view books")
    print("  ✓ Users add books to reading lists")
    print("  ✓ Users manage their reading progress")
    print("=" * 60)
    
    # Test connection
    if not test_connection():
        print("❌ MongoDB connection failed")
        return
    
    # Create a real user account
    print("\n👤 STEP 1: User Registration")
    print("-" * 30)
    register_result = register(
        username="bookworm",
        password="securepass",
        email="<EMAIL>",
        full_name="<PERSON> Bookworm",
        preferences={"reading_goal": 30}
    )
    print(f"Registration: {register_result}")
    
    login_result = login("bookworm", "securepass")
    print(f"Login: {'Success' if login_result else 'Failed'}")
    
    # Real books that user might encounter
    real_books = [
        {
            "isbn": "*************",
            "title": "Educated",
            "author": "Tara Westover",
            "cover_url": "https://images.example.com/educated.jpg",
            "description": "A memoir about education, family, and the struggle between loyalty and independence.",
            "genre": "Memoir",
            "publication_year": 2018,
            "publisher": "Random House",
            "page_count": 334,
            "rating": 4.4
        },
        {
            "isbn": "9780525559474",
            "title": "Becoming",
            "author": "Michelle Obama",
            "cover_url": "https://images.example.com/becoming.jpg",
            "description": "The memoir of former First Lady Michelle Obama.",
            "genre": "Biography",
            "publication_year": 2018,
            "publisher": "Crown Publishing",
            "page_count": 448,
            "rating": 4.6
        },
        {
            "isbn": "9780735219090",
            "title": "Where the Crawdads Sing",
            "author": "Delia Owens",
            "cover_url": "https://images.example.com/crawdads.jpg",
            "description": "A mystery and coming-of-age story set in the marshes of North Carolina.",
            "genre": "Fiction",
            "publication_year": 2018,
            "publisher": "G.P. Putnam's Sons",
            "page_count": 370,
            "rating": 4.3
        }
    ]
    
    # User browses and views books
    print(f"\n📖 STEP 2: User Browses Books")
    print("-" * 30)
    
    for i, book in enumerate(real_books, 1):
        print(f"\n📚 Book {i}: User views '{book['title']}'")
        
        # User views book - complete details stored automatically
        view_result = add_viewed_book("bookworm", book)
        print(f"   ✅ {view_result}")
        print(f"   📊 Stored: {book['genre']}, {book['page_count']} pages, ⭐{book['rating']}")
        
        # User adds some books to reading list
        if i <= 2:  # Add first 2 books to reading list
            status = "to_read" if i == 1 else "reading"
            readlist_result = add_to_readlist("bookworm", book, status)
            print(f"   📋 {readlist_result} (Status: {status})")
    
    # User views a book again (should update view count)
    print(f"\n🔄 STEP 3: User Re-views a Book")
    print("-" * 30)
    print(f"User views '{real_books[0]['title']}' again...")
    view_again_result = add_viewed_book("bookworm", real_books[0])
    print(f"✅ {view_again_result}")
    
    # Show viewing history with complete details
    print(f"\n📈 STEP 4: User's Complete Viewing History")
    print("-" * 30)
    viewed_books = get_viewed_books("bookworm")
    
    for book in viewed_books:
        print(f"\n📚 {book['title']} by {book['author']}")
        print(f"   📊 {book['genre']} | {book['page_count']} pages | ⭐{book['rating']}")
        print(f"   👀 Viewed {book['view_count']} time(s)")
        print(f"   📝 {book['description'][:60]}...")
        print(f"   🏢 Published by {book['publisher']} ({book['publication_year']})")
    
    # Show reading list with complete details
    print(f"\n📋 STEP 5: User's Reading List")
    print("-" * 30)
    readlist = get_readlist("bookworm")
    
    for item in readlist:
        if item.get('type') == 'book':
            print(f"\n📖 {item['title']} by {item['author']}")
            print(f"   📊 Status: {item['status']} | Genre: {item['genre']}")
            print(f"   📄 {item['page_count']} pages | ⭐{item['rating']}")
            print(f"   📅 Added: {item['date_added'].strftime('%Y-%m-%d %H:%M')}")
    
    # User updates reading progress
    print(f"\n📝 STEP 6: User Updates Reading Progress")
    print("-" * 30)
    if readlist:
        first_book_isbn = readlist[0]['isbn']
        update_result = update_status("bookworm", first_book_isbn, "completed")
        print(f"✅ {update_result}")
    
    # Show user profile
    print(f"\n👤 STEP 7: User Profile Summary")
    print("-" * 30)
    profile = get_user_profile("bookworm")
    if profile:
        print(f"Name: {profile['full_name']}")
        print(f"Email: {profile['email']}")
        print(f"Reading Goal: {profile['preferences']['reading_goal']} books/year")
        print(f"Member Since: {profile['date_registered'].strftime('%Y-%m-%d')}")
    
    print(f"\n" + "=" * 60)
    print("✅ DEMO COMPLETE - REAL USAGE SUCCESSFUL!")
    print("✅ Complete book details stored when users:")
    print("   📖 View books (with view count tracking)")
    print("   📋 Add books to reading lists")
    print("   📊 All metadata preserved (genre, pages, rating, etc.)")
    print("   🔄 No external API calls needed - all data stored locally")
    print("=" * 60)

if __name__ == "__main__":
    demonstrate_real_usage()
