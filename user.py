from config import get_database
import hashlib
from datetime import datetime

# Get database connection
db = get_database()
if db is None:
    raise Exception("Failed to connect to MongoDB")

users = db["users"]

def register(username, password, email=None, full_name=None, preferences=None):
    """
    Register a new user with detailed information

    Args:
        username (str): Unique username
        password (str): User password
        email (str, optional): User email
        full_name (str, optional): User's full name
        preferences (dict, optional): User preferences like favorite genres, etc.
    """
    if users.find_one({"username": username}):
        return "Username already exists"

    hashed = hashlib.sha256(password.encode()).hexdigest()

    user_data = {
        "username": username,
        "password": hashed,
        "email": email,
        "full_name": full_name,
        "preferences": preferences or {},
        "date_registered": datetime.now(),
        "last_login": None,
        "profile": {
            "favorite_genres": [],
            "favorite_authors": [],
            "reading_goal": None,
            "books_read_count": 0,
            "total_pages_read": 0
        },
        "settings": {
            "privacy": "public",
            "email_notifications": True,
            "reading_reminders": False
        }
    }

    users.insert_one(user_data)
    return "Registered successfully"

def login(username, password):
    """Login user and update last login time"""
    hashed = hashlib.sha256(password.encode()).hexdigest()
    user = users.find_one({"username": username, "password": hashed})

    if user:
        # Update last login time
        users.update_one(
            {"username": username},
            {"$set": {"last_login": datetime.now()}}
        )
        return True
    return False

def get_user_profile(username):
    """Get complete user profile information"""
    user = users.find_one({"username": username}, {"password": 0})  # Exclude password
    return user

def update_user_profile(username, updates):
    """Update user profile information"""
    # Remove sensitive fields that shouldn't be updated this way
    safe_updates = {k: v for k, v in updates.items() if k not in ["username", "password", "date_registered"]}

    result = users.update_one(
        {"username": username},
        {"$set": safe_updates}
    )

    if result.matched_count == 0:
        return "User not found"
    return "Profile updated successfully"

def update_reading_stats(username, pages_read=0, books_completed=0):
    """Update user's reading statistics"""
    users.update_one(
        {"username": username},
        {
            "$inc": {
                "profile.books_read_count": books_completed,
                "profile.total_pages_read": pages_read
            }
        }
    )