#!/usr/bin/env python3
"""
Clean Database Script

This script removes all test data and provides a clean database
for real usage demonstration.
"""

from config import get_database

def clean_all_collections():
    """Remove all test data from MongoDB collections"""
    
    print("🧹 CLEANING DATABASE")
    print("=" * 40)
    
    # Get database
    db = get_database()
    if not db:
        print("❌ Failed to connect to MongoDB")
        return
    
    # Get all collections
    collections = {
        "users": db["users"],
        "books": db["books"],
        "viewed_books": db["viewed_books"],
        "readlist": db["readlist"]
    }
    
    print("Removing all test data...")
    
    for collection_name, collection in collections.items():
        # Count documents before deletion
        before_count = collection.count_documents({})
        
        # Delete all documents
        result = collection.delete_many({})
        
        print(f"   {collection_name}: {before_count} documents → {result.deleted_count} deleted")
    
    print("\n✅ Database cleaned successfully!")
    print("✅ Ready for real usage demonstration")
    print("=" * 40)

def verify_clean_database():
    """Verify that the database is clean"""
    
    print("\n🔍 VERIFYING CLEAN DATABASE")
    print("=" * 40)
    
    db = get_database()
    if not db:
        print("❌ Failed to connect to MongoDB")
        return
    
    collections = ["users", "books", "viewed_books", "readlist"]
    
    all_clean = True
    for collection_name in collections:
        count = db[collection_name].count_documents({})
        print(f"   {collection_name}: {count} documents")
        if count > 0:
            all_clean = False
    
    if all_clean:
        print("\n✅ Database is completely clean!")
    else:
        print("\n⚠️ Some data still exists in database")
    
    print("=" * 40)

if __name__ == "__main__":
    clean_all_collections()
    verify_clean_database()
