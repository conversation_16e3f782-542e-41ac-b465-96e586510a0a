from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib.auth.forms import UserCreationForm
from django.contrib import messages
from django.http import JsonResponse
import sys
import os

# Add parent directory to path for MongoDB modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

try:
    from mongodb_django_integration import MongoDBUserManager, MongoDBBookManager
except ImportError:
    MongoDBUserManager = None
    MongoDBBookManager = None

def home(request):
    context = {}

    # If user is logged in, get their MongoDB data
    if request.user.is_authenticated and MongoDBUserManager:
        profile = MongoDBUserManager.get_user_profile(request.user)
        if profile:
            context['user_profile'] = profile

        # Get recent viewed books
        if MongoDBBookManager:
            recent_books = MongoDBBookManager.get_user_viewed_books(request.user)[:5]
            context['recent_books'] = recent_books

    return render(request, 'books/home.html', context)

def register(request):
    if request.method == 'POST':
        form = UserCreationForm(request.POST)
        if form.is_valid():
            user = form.save()
            username = form.cleaned_data.get('username')

            # Create MongoDB profile for new user
            if MongoDBUserManager:
                MongoDBUserManager.create_user_profile(user)
                messages.success(request, f'Account created for {username}! MongoDB profile created.')
            else:
                messages.success(request, f'Account created for {username}!')

            return redirect('login')
    else:
        form = UserCreationForm()
    return render(request, 'books/register.html', {'form': form})

@login_required
def recommendations(request):
    context = {}

    if MongoDBBookManager:
        # Get user's viewed books for recommendations
        viewed_books = MongoDBBookManager.get_user_viewed_books(request.user)
        context['viewed_books'] = viewed_books

        # Get user's favorite genres for recommendations
        profile = MongoDBUserManager.get_user_profile(request.user)
        if profile:
            favorite_genres = profile.get('profile', {}).get('favorite_genres', [])
            context['favorite_genres'] = favorite_genres

    return render(request, 'books/recommendations.html', context)

@login_required
def readlist(request):
    context = {}

    if MongoDBBookManager:
        # Get user's reading list from MongoDB
        user_readlist = MongoDBBookManager.get_user_readlist(request.user)
        context['readlist'] = user_readlist

        # Organize by status
        to_read = [book for book in user_readlist if book.get('status') == 'to_read']
        reading = [book for book in user_readlist if book.get('status') == 'reading']
        completed = [book for book in user_readlist if book.get('status') == 'completed']

        context.update({
            'to_read': to_read,
            'reading': reading,
            'completed': completed
        })

    return render(request, 'books/readlist.html', context)

@login_required
def add_to_readlist_view(request):
    """Add a book to user's reading list via POST request"""
    if request.method == 'POST' and MongoDBBookManager:
        book_data = {
            'isbn': request.POST.get('isbn'),
            'title': request.POST.get('title'),
            'author': request.POST.get('author'),
            'cover_url': request.POST.get('cover_url', ''),
            'description': request.POST.get('description', ''),
            'genre': request.POST.get('genre', ''),
        }

        status = request.POST.get('status', 'to_read')

        result = MongoDBBookManager.add_to_readlist(request.user, book_data, status)
        messages.success(request, result)

        return redirect('readlist')

    return redirect('search')

@login_required
def view_book(request):
    """Record a book view and display book details"""
    if request.method == 'POST' and MongoDBBookManager:
        book_data = {
            'isbn': request.POST.get('isbn'),
            'title': request.POST.get('title'),
            'author': request.POST.get('author'),
            'cover_url': request.POST.get('cover_url', ''),
            'description': request.POST.get('description', ''),
            'genre': request.POST.get('genre', ''),
        }

        result = MongoDBBookManager.add_viewed_book(request.user, book_data)

        return JsonResponse({
            'status': 'success',
            'message': result,
            'book': book_data
        })

    return JsonResponse({'status': 'error', 'message': 'Invalid request'})

def search(request):
    context = {}

    # If user is logged in, show their search history
    if request.user.is_authenticated and MongoDBBookManager:
        recent_views = MongoDBBookManager.get_user_viewed_books(request.user)[:10]
        context['recent_views'] = recent_views

    return render(request, 'books/search.html', context)

@login_required
def profile(request):
    """User profile page with MongoDB data"""
    context = {}

    if MongoDBUserManager:
        profile = MongoDBUserManager.get_user_profile(request.user)
        if not profile:
            # Create profile if it doesn't exist
            MongoDBUserManager.create_user_profile(request.user)
            profile = MongoDBUserManager.get_user_profile(request.user)

        context['mongodb_profile'] = profile

        # Get reading statistics
        if MongoDBBookManager:
            readlist = MongoDBBookManager.get_user_readlist(request.user)
            viewed_books = MongoDBBookManager.get_user_viewed_books(request.user)

            completed_books = [book for book in readlist if book.get('status') == 'completed']

            context.update({
                'total_books_in_readlist': len(readlist),
                'books_completed': len(completed_books),
                'books_viewed': len(viewed_books),
                'recent_activity': viewed_books[:5]
            })

    return render(request, 'books/profile.html', context)

