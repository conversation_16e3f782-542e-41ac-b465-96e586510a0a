#!/usr/bin/env python3
"""
Enhanced MongoDB Storage Test Script

This script demonstrates the enhanced MongoDB storage system that stores
complete book details when users view books, add to readlist, and user details.
"""

from config import test_connection, get_collections
from user import register, login, get_user_profile, update_user_profile, update_reading_stats
from book import add_book, get_book
from readlist import add_to_readlist, get_readlist, update_status
from viewed import add_viewed_book, get_viewed_books, get_recently_viewed, get_most_viewed_books

def main():
    print("=" * 80)
    print("ENHANCED MONGODB STORAGE TEST")
    print("Testing complete book details storage in all collections")
    print("=" * 80)
    
    # Test MongoDB connection
    print("\n1. Testing MongoDB Connection...")
    if not test_connection():
        print("❌ MongoDB connection failed. Exiting.")
        return
    
    # Test enhanced user registration
    print("\n2. Testing Enhanced User Registration...")
    user_result = register(
        username="bookworm_user",
        password="securepass123",
        email="<EMAIL>",
        full_name="Jane Bookworm",
        preferences={"favorite_genres": ["Fiction", "Mystery"], "reading_goal": 50}
    )
    print(f"   Registration result: {user_result}")
    
    # Test login
    print("\n3. Testing User Login...")
    login_result = login("bookworm_user", "securepass123")
    print(f"   Login result: {login_result}")
    
    # Get user profile
    print("\n4. Testing User Profile Retrieval...")
    profile = get_user_profile("bookworm_user")
    if profile:
        print(f"   User: {profile.get('full_name')} ({profile.get('username')})")
        print(f"   Email: {profile.get('email')}")
        print(f"   Registered: {profile.get('date_registered')}")
        print(f"   Reading Goal: {profile.get('preferences', {}).get('reading_goal')}")
    
    # Test enhanced book storage
    print("\n5. Testing Enhanced Book Storage...")
    book_result = add_book(
        isbn="9780451524935",
        title="1984",
        author="George Orwell",
        cover_url="https://example.com/1984-cover.jpg",
        description="A dystopian social science fiction novel and cautionary tale.",
        genre="Dystopian Fiction",
        publication_year=1949,
        publisher="Secker & Warburg",
        page_count=328,
        rating=4.5
    )
    print(f"   Add book result: {book_result}")
    
    # Test viewing book with complete details
    print("\n6. Testing Enhanced Book Viewing Storage...")
    complete_book_data = {
        "isbn": "9780451524935",
        "title": "1984",
        "author": "George Orwell",
        "cover_url": "https://example.com/1984-cover.jpg",
        "description": "A dystopian social science fiction novel and cautionary tale.",
        "genre": "Dystopian Fiction",
        "publication_year": 1949,
        "publisher": "Secker & Warburg",
        "page_count": 328,
        "rating": 4.5
    }
    
    view_result = add_viewed_book("bookworm_user", complete_book_data)
    print(f"   View book result: {view_result}")
    
    # View the same book again to test update functionality
    view_result2 = add_viewed_book("bookworm_user", complete_book_data)
    print(f"   Second view result: {view_result2}")
    
    # Test enhanced readlist storage
    print("\n7. Testing Enhanced Readlist Storage...")
    readlist_result = add_to_readlist("bookworm_user", complete_book_data, "reading")
    print(f"   Add to readlist result: {readlist_result}")
    
    # Add another book with different details
    print("\n8. Adding Another Book...")
    book2_data = {
        "isbn": "9780061120084",
        "title": "To Kill a Mockingbird",
        "author": "Harper Lee",
        "cover_url": "https://example.com/mockingbird-cover.jpg",
        "description": "A novel about racial injustice and childhood in the American South.",
        "genre": "Classic Literature",
        "publication_year": 1960,
        "publisher": "J.B. Lippincott & Co.",
        "page_count": 376,
        "rating": 4.3
    }
    
    add_book(**book2_data)
    add_viewed_book("bookworm_user", book2_data)
    add_to_readlist("bookworm_user", book2_data, "to_read")
    
    # Test retrieval functions
    print("\n9. Testing Enhanced Data Retrieval...")
    
    # Get viewed books with complete details
    viewed_books = get_viewed_books("bookworm_user")
    print(f"   Viewed books count: {len(viewed_books)}")
    for book in viewed_books:
        print(f"     - {book.get('title')} by {book.get('author')}")
        print(f"       Genre: {book.get('genre')}, Pages: {book.get('page_count')}")
        print(f"       View count: {book.get('view_count')}, Last viewed: {book.get('viewed_at')}")
    
    # Get readlist with complete details
    readlist = get_readlist("bookworm_user")
    print(f"\n   Readlist items count: {len(readlist)}")
    for item in readlist:
        print(f"     - {item.get('title')} by {item.get('author')}")
        print(f"       Status: {item.get('status')}, Genre: {item.get('genre')}")
        print(f"       Description: {item.get('description')[:50]}...")
    
    # Test reading statistics update
    print("\n10. Testing Reading Statistics Update...")
    update_reading_stats("bookworm_user", pages_read=328, books_completed=1)
    
    # Update user profile
    profile_updates = {
        "profile.favorite_authors": ["George Orwell", "Harper Lee"],
        "profile.favorite_genres": ["Dystopian Fiction", "Classic Literature"]
    }
    update_result = update_user_profile("bookworm_user", profile_updates)
    print(f"    Profile update result: {update_result}")
    
    # Get updated profile
    updated_profile = get_user_profile("bookworm_user")
    if updated_profile:
        print(f"    Books read: {updated_profile.get('profile', {}).get('books_read_count')}")
        print(f"    Pages read: {updated_profile.get('profile', {}).get('total_pages_read')}")
        print(f"    Favorite authors: {updated_profile.get('profile', {}).get('favorite_authors')}")
    
    print("\n" + "=" * 80)
    print("✅ ENHANCED STORAGE TEST COMPLETED SUCCESSFULLY!")
    print("✅ All book details, user details, and reading data stored in MongoDB")
    print("=" * 80)

if __name__ == "__main__":
    main()
